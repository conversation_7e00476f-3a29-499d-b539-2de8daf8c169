import { drizzle } from 'drizzle-orm/d1';
import { AppEnv } from '../binding';
import { schema } from '../db';
import { betterAuth } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';

// Single auth configuration that handles both CLI and runtime scenarios
export const createAuth = (env?: AppEnv) => {
	// Use actual DB for runtime, empty object for CLI
	const db = env ? drizzle(env.Bindings.DB, { schema, logger: true }) : ({} as any);

	return betterAuth({
		emailAndPassword: {
			enabled: true,
		},
		...(env
			? {
					database: db,
					options: {
						usePlural: true,
						debugLogs: true,
					},
				}
			: {
					database: drizzleAdapter({} as D1Database, {
						provider: 'sqlite',
						usePlural: true,
						debugLogs: true,
					}),
				}),
	});
};

// Export for CLI schema generation
export const auth = createAuth();
