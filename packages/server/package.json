{"name": "server", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "drizzle-kit": "^0.31.1", "tsx": "^4.19.4", "typescript": "^5.5.2", "vitest": "~3.0.7", "wrangler": "^4.18.0"}, "dependencies": {"@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/openai-compatible": "^0.2.14", "ai": "^4.3.16", "better-auth-cloudflare": "^0.2.0", "cloudflare": "^4.4.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.1", "hono": "^4.7.10", "superjson": "^2.2.2", "uuid": "^11.1.0"}}